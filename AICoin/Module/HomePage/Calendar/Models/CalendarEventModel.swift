//
//  CalendarEventModel.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-18.
//  Copyright © 2024 AICoin. All rights reserved.
//

import EventKit
import Foundation

/// 日历事件相关入口模型
struct CalendarRelatedEntrance {
    var entranceType: Int
    var name: String = ""  // entranceType=0 时使用
    var link: String = ""  // entranceType=0 时使用
    var coinShow: String = ""  // entranceType=1 时使用
    var marketName: String = ""  // entranceType=1 时使用
    var key: String = ""  // entranceType=1 时使用

    var displayText: String {
        if entranceType == 1 {
            return "\(coinShow) \(marketName)"
        } else {
            return name
        }
    }
}

/// 日历事件模型
struct CalendarEventModel {
    var id: String = UUID().uuidString
    var title: String
    var date: Date
    var content: String?
    var relatedEntrances: [CalendarRelatedEntrance] = []
    var isPending: Bool = false
    var isRemind: Bool = false
    var score: Int = 0
    var startDate: TimeInterval = 0
    var type: String
    // 计算属性保持兼容性
    var computedIsImportant: Bool {
        return score >= 3
    }

    /// 判断事件是否已过期
    var isExpired: Bool {
        let today = Date()
        return date < today
    }
}

/// 日历事件请求模型
struct CalendarEventRequestModel {
    var startTime: TimeInterval
    var period: Int = 1
    var lan: String = "cn"
    var returnType: [String] = ["event", "flash"]
    var words: [String] = []

    init(date: Date, words: [String] = []) {
        self.startTime = Calendar.current.startOfDay(for: date).timeIntervalSince1970 * 1000
        self.words = words
    }

    init(startTime: TimeInterval, words: [String] = []) {
        self.startTime = startTime
        self.words = words
    }
}

// MARK: - 日期工具扩展
extension CalendarEventRequestModel {
    /// 从Date对象获取当天0点的毫秒时间戳
    static func millisecondTimestamp(from date: Date) -> TimeInterval {
        return Calendar.current.startOfDay(for: date).timeIntervalSince1970 * 1000
    }

    /// 从毫秒时间戳转换为Date对象
    static func date(from milliseconds: TimeInterval) -> Date {
        return Date(timeIntervalSince1970: milliseconds / 1000.0)
    }
}

/// 日历事件管理器
class CalendarEventManager {
    static let shared = CalendarEventManager()

    // 页面级内存缓存
    private var sessionCache: [String: [CalendarEventModel]] = [:]
    private var wordsCache: [String]?
    // 日历标记缓存
    private var marksCache: [String: [[String: Any]]] = [:]

    private init() {}

    /// 清除页面缓存（页面退出时调用）
    func clearSessionCache() {
        sessionCache.removeAll()
        wordsCache = nil
        marksCache.removeAll()
    }

    /// 获取指定日期的事件
    func getDayEvents(
        request: CalendarEventRequestModel,
        completion: @escaping ([CalendarEventModel]?, Error?) -> Void
    ) {
        // 检查页面级缓存
        let cacheKey = "calendar_\(Int(request.startTime))_\(request.words.joined(separator: "_"))"
        if let cachedEvents = sessionCache[cacheKey] {
            completion(cachedEvents, nil)
            return
        }
        print("发起事件列表请求")
        // 构造请求参数
        let params: [String: Any] = [
            "startTime": Int(request.startTime),
            "period": request.period,
            "lan": request.lan,
            "returnType": request.returnType,
            "words": request.words,
        ]

        AICHttpManager.shared.post(
            "https://api-test.aicoin.com/api/upgrade/calendar/list", parameters: params,
            progress: nil,
            success: { [weak self] task, response in
                let json = JSON(response ?? "")

                if json["success"].boolValue {
                    let dataJson = json["data"]
                    let listArray = dataJson["list"].arrayValue

                    var events: [CalendarEventModel] = []
                    for itemJson in listArray {
                        let apiItem = CalendarEventAPIItem()
                        apiItem.content = itemJson["content"].stringValue
                        apiItem.id = itemJson["id"].stringValue
                        apiItem.isPending = itemJson["isPending"].stringValue
                        apiItem.isRemind = itemJson["isRemind"].stringValue
                        apiItem.score = itemJson["score"].stringValue
                        apiItem.startDate = itemJson["startDate"].numberValue
                        apiItem.title = itemJson["title"].stringValue
                        apiItem.type = itemJson["type"].stringValue

                        // 解析relatedEntrances数组
                        let relatedEntrancesArray = itemJson["relatedEntrances"].arrayValue
                        var relatedEntrances: [CalendarEventAPIRelatedEntrance] = []
                        for entranceJson in relatedEntrancesArray {
                            let entrance = CalendarEventAPIRelatedEntrance()
                            entrance.entranceType = entranceJson["entranceType"].intValue
                            entrance.name = entranceJson["name"].stringValue
                            entrance.link = entranceJson["link"].stringValue
                            entrance.coinShow = entranceJson["coinShow"].stringValue
                            entrance.marketName = entranceJson["marketName"].stringValue
                            entrance.key = entranceJson["key"].stringValue
                            relatedEntrances.append(entrance)
                        }
                        apiItem.relatedEntrances = relatedEntrances

                        let event = CalendarEventModel.fromAPIItem(apiItem)
                        events.append(event)
                    }

                    // 缓存到页面级缓存
                    self?.sessionCache[cacheKey] = events
                    completion(events, nil)
                } else {
                    let errorCode = json["errorCode"].intValue
                    let errorMessage = json["error"].stringValue
                    let error = NSError(
                        domain: "APIError", code: errorCode,
                        userInfo: [NSLocalizedDescriptionKey: errorMessage])
                    completion(nil, error)
                }
            },
            failure: { task, error in
                completion(nil, error)
            })
    }

    /// 获取事件分类words列表
    func getEventWords(completion: @escaping ([String]?, Error?) -> Void) {
        print("getEventWords")
        // 检查页面级缓存
        if let cachedWords = wordsCache {
            completion(cachedWords, nil)
            return
        }

        AICHttpManager.shared.post(
            "https://api-test.aicoin.com/api/upgrade/calendar/words", parameters: nil,
            progress: nil,
            success: { [weak self] task, response in
                let json = JSON(response ?? "")

                if json["success"].boolValue {
                    let words = json["data"]["words"].arrayValue.map { $0.stringValue }
                    // 缓存到页面级缓存
                    self?.wordsCache = words
                    completion(words, nil)
                } else {
                    let errorCode = json["errorCode"].intValue
                    let errorMessage = json["error"].stringValue
                    let error = NSError(
                        domain: "APIError", code: errorCode,
                        userInfo: [NSLocalizedDescriptionKey: errorMessage])
                    completion(nil, error)
                }
            },
            failure: { task, error in
                completion(nil, error)
            })
    }

    /// 获取日历标记数据
    func getCalendarMarks(
        startTime: TimeInterval, endTime: TimeInterval,
        completion: @escaping ([[String: Any]]?, Error?) -> Void
    ) {
        // 检查页面级缓存
        print("getCalendarMarks startTime: \(startTime), endTime: \(endTime)")
        let cacheKey = "calendar_marks_\(Int(startTime))_\(Int(endTime))"
        if let cachedMarks = marksCache[cacheKey] {
            completion(cachedMarks, nil)
            return
        }

        // 构造请求参数
        let params: [String: Any] = [
            "startTime": Int(startTime),
            "endTime": Int(endTime),
            "returnType": ["event", "flash"],
        ]

        AICHttpManager.shared.post(
            "/api/upgrade/calendar/marks", parameters: params, progress: nil,
            success: { [weak self] task, response in
                let json = JSON(response ?? "")

                if json["success"].boolValue {
                    let dataArray = json["data"].arrayValue

                    var marks: [[String: Any]] = []
                    for itemJson in dataArray {
                        let mark: [String: Any] = [
                            "time": itemJson["time"].doubleValue,
                            "score": Int(itemJson["score"].stringValue) ?? 0,
                        ]
                        marks.append(mark)
                    }

                    // 缓存到页面级缓存
                    self?.marksCache[cacheKey] = marks

                    completion(marks, nil)
                } else {
                    let errorCode = json["errorCode"].intValue
                    let errorMessage = json["error"].stringValue
                    let error = NSError(
                        domain: "APIError", code: errorCode,
                        userInfo: [NSLocalizedDescriptionKey: errorMessage])
                    completion(nil, error)
                }
            },
            failure: { task, error in
                completion(nil, error)
            })
    }

    /// 保存事件提醒设置
    /// - Parameters:
    ///   - eventId: 事件ID
    ///   - startDate: 事件开始时间
    ///   - reminderTime: 提醒提前时间（秒数）
    ///   - isApp: 是否APP提醒
    ///   - isPc: 是否PC提醒
    ///   - isEmail: 是否邮件提醒
    ///   - remarks: 备注
    ///   - type: 事件类型
    ///   - completion: 完成回调
    func saveEventReminder(
        eventId: String,
        startDate: TimeInterval,
        reminderTime: Int,
        isApp: Bool,
        isPc: Bool,
        isEmail: Bool,
        remarks: String,
        type: String,
        completion: @escaping (Bool, Error?) -> Void
    ) {
        // 构造请求参数
        let parameters: [String: Any] = [
            "time": reminderTime,
            "id": eventId,
            "isApp": isApp ? 1 : 0,
            "isPc": isPc ? 1 : 0,
            "isEmail": isEmail ? 1 : 0,
            "remarks": remarks,
            "type": type,
            "startDate": Int(startDate),
        ]

        // 发起网络请求
        AICHttpManager.shared.post(
            "/api/upgrade/calendar/editRemind",
            parameters: parameters,
            progress: nil,
            success: { task, response in
                let json = JSON(response ?? "")

                if json["success"].boolValue {
                    // 请求成功
                    let data = json["data"]
                    print("提醒设置成功: \(data)")
                    completion(true, nil)
                } else {
                    // 请求失败
                    let errorCode = json["errorCode"].intValue
                    let errorMessage = json["error"].stringValue
                    let error = NSError(
                        domain: "APIError",
                        code: errorCode,
                        userInfo: [NSLocalizedDescriptionKey: errorMessage]
                    )
                    completion(false, error)
                }
            },
            failure: { task, error in
                completion(false, error)
            }
        )
    }

    // MARK: - 日历同步功能

    /// 请求日历访问权限
    /// - Parameter completion: 权限请求结果回调
    func requestCalendarAccess(completion: @escaping (Bool, Error?) -> Void) {
        let eventStore = EKEventStore()

        if #available(iOS 17.0, *) {
            eventStore.requestFullAccessToEvents { granted, error in
                DispatchQueue.main.async {
                    completion(granted, error)
                }
            }
        } else {
            eventStore.requestAccess(to: .event) { granted, error in
                DispatchQueue.main.async {
                    completion(granted, error)
                }
            }
        }
    }

    /// 检查日历访问权限状态
    /// - Returns: 权限状态
    func checkCalendarAuthorizationStatus() -> EKAuthorizationStatus {
        if #available(iOS 17.0, *) {
            return EKEventStore.authorizationStatus(for: .event)
        } else {
            return EKEventStore.authorizationStatus(for: .event)
        }
    }

    /// 添加事件到系统日历
    /// - Parameters:
    ///   - event: 日历事件模型
    ///   - reminderTime: 提醒提前时间（秒数）
    ///   - completion: 添加结果回调
    func addEventToSystemCalendar(
        event: CalendarEventModel,
        reminderTime: Int,
        completion: @escaping (Bool, Error?) -> Void
    ) {
        // 首先检查权限
        let authStatus = checkCalendarAuthorizationStatus()

        switch authStatus {
        case .notDetermined:
            // 权限未确定，请求权限
            requestCalendarAccess { [weak self] granted, error in
                if granted {
                    self?.performAddEventToCalendar(
                        event: event, reminderTime: reminderTime, completion: completion)
                } else {
                    let error =
                        error
                        ?? NSError(
                            domain: "CalendarError",
                            code: -1,
                            userInfo: [NSLocalizedDescriptionKey: "日历访问权限被拒绝"]
                        )
                    completion(false, error)
                }
            }

        case .authorized, .fullAccess:
            // 已授权，直接添加事件
            performAddEventToCalendar(
                event: event, reminderTime: reminderTime, completion: completion)

        case .denied, .restricted:
            // 权限被拒绝或受限
            let error = NSError(
                domain: "CalendarError",
                code: -2,
                userInfo: [NSLocalizedDescriptionKey: "日历访问权限被拒绝，请在设置中开启"]
            )
            completion(false, error)

        case .writeOnly:
            // 只有写权限（iOS 17+）
            performAddEventToCalendar(
                event: event, reminderTime: reminderTime, completion: completion)

        @unknown default:
            let error = NSError(
                domain: "CalendarError",
                code: -3,
                userInfo: [NSLocalizedDescriptionKey: "未知的权限状态"]
            )
            completion(false, error)
        }
    }

    /// 执行添加事件到日历的操作
    /// - Parameters:
    ///   - event: 日历事件模型
    ///   - reminderTime: 提醒提前时间（秒数）
    ///   - completion: 添加结果回调
    private func performAddEventToCalendar(
        event: CalendarEventModel,
        reminderTime: Int,
        completion: @escaping (Bool, Error?) -> Void
    ) {
        let eventStore = EKEventStore()

        // 创建日历事件
        let calendarEvent = EKEvent(eventStore: eventStore)
        calendarEvent.title = event.title
        calendarEvent.startDate = event.date
        calendarEvent.endDate = event.date.addingTimeInterval(3600)  // 默认1小时时长
        calendarEvent.notes = event.content
        calendarEvent.calendar = eventStore.defaultCalendarForNewEvents

        // 添加提醒
        if reminderTime > 0 {
            let alarm = EKAlarm(relativeOffset: -TimeInterval(reminderTime))
            calendarEvent.addAlarm(alarm)
        }

        // 保存事件
        do {
            try eventStore.save(calendarEvent, span: .thisEvent)
            completion(true, nil)
        } catch {
            completion(false, error)
        }
    }
}

// MARK: - 数据转换扩展
extension CalendarEventModel {
    /// 从API项目模型转换为业务模型
    static func fromAPIItem(_ apiItem: CalendarEventAPIItem) -> CalendarEventModel {
        var model = CalendarEventModel(
            title: apiItem.title,
            date: Date(timeIntervalSince1970: apiItem.startDate.doubleValue / 1000.0),
            type: apiItem.type
        )

        model.id = apiItem.id
        model.content = apiItem.content.isEmpty ? nil : apiItem.content
        model.isPending = apiItem.isPending == "1"
        model.isRemind = apiItem.isRemind == "1"
        model.score = Int(apiItem.score) ?? 0
        model.startDate = apiItem.startDate.doubleValue

        // 转换relatedEntrances
        model.relatedEntrances = apiItem.relatedEntrances.map { apiEntrance in
            CalendarRelatedEntrance(
                entranceType: apiEntrance.entranceType,
                name: apiEntrance.name,
                link: apiEntrance.link,
                coinShow: apiEntrance.coinShow,
                marketName: apiEntrance.marketName,
                key: apiEntrance.key
            )
        }
        return model
    }
}
